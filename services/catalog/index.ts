import { Elysia, t } from 'elysia'
import setupSwagger from '../../shared/setup/swagger'
import setupEnv from '../../shared/setup/env'
import * as bun from 'bun'
import { get_catalog_by_id_response } from './models/get-catalog-by-id'
import { authentication } from '../../shared/setup/authentication'
import { create_db } from './database/db'
import CatalogService from './plugins/catalog-plugin'
import { get_content_stream } from './models/get-content-stream'

// All routes MUST be listed here in the same level,
// while handler should be defined as service plugins

const catalog_app = new Elysia({
	normalize: 'typebox'
})
	.trace(async ({ onHandle }) => {
		onHandle(({ begin, onStop }) => {
			onStop(({ end }) => {
				console.log('handle took', end - begin, 'ms')
			})
		})
	})
	.use(setupEnv())
	.use(
		setupSwagger({
			service_name: bun.env.CATALOG_SERVICE_NAME || ''
		})
	)
	.decorate('db', create_db())
	.use(authentication)
	.get(
		'/',
		({ env }) => `Hello Suara 🎵 - ${env.CATALOG_SERVICE_NAME} service!`
	)
	.get(
		'/catalog/:id',
		async ({ params, db }) => {
			return CatalogService.getCatalogById({ id: params.id }, { db })
		},
		{
			response: get_catalog_by_id_response,
			getUserAuthentication: true
		}
	)
	.get(
		'/catalog',
		async ({ query, db }) => {
			return CatalogService.getCatalogByIds(
				{ ids: query.ids.split(',') },
				{ db }
			)
		},
		{
			query: t.Object({
				ids: t.String({
					description: 'Comma separated list of catalog ids'
				})
			})
		}
	)
	.post(
		'/content/:id/stream',
		async ({ params, db, user_id, auth_key }) => {
			console.log({ user_id })
			return CatalogService.getContentStream(
				{
					id: params.id,
					user_id: user_id || ''
				},
				{ db, bearer_token: auth_key }
			)
		},
		{
			response: t.Union([
				get_content_stream,
				t.Object({ message: t.String() })
			]),
			getUserAuthentication: true
		}
	)
	.listen(bun.env.CATALOG_PORT || 3000)

console.log(
	`🦊 Elysia is running at ${catalog_app.server?.hostname}:${catalog_app.server?.port}`
)

export type CatalogApp = typeof catalog_app
