import { IMicroserviceContext } from '../../../shared/interfaces'
import { GetCatalogByIdResponse } from '../models/get-catalog-by-id'
import {
	catalog,
	catalog_genre,
	catalog_user,
	content,
	genre,
	user_premium_content
} from '../database/schema'
import { and, count, eq, inArray, isNull } from 'drizzle-orm'
import { take_uniq_or_throw } from '../database/utils'
import { UserApp } from '@noice/user-service'
import { treaty } from '@elysiajs/eden'
import { GetContentStream } from '../models/get-content-stream'
import { NotFoundError } from 'elysia'
import { SubscriptionApp } from '@noice/subscription-service'

abstract class CatalogService {
	static async getCatalogById(
		{ id }: { id: string },
		{ db }: IMicroserviceContext
	): Promise<GetCatalogByIdResponse> {
		const catalog_resp = await db
			.select()
			.from(catalog)
			.where(and(eq(catalog.id, id), eq(catalog.status, 'active')))
			.execute()
			.then(take_uniq_or_throw)

		const content_count = await db
			.select({
				count: count(content.id)
			})
			.from(content)
			.where(
				and(
					eq(content.catalog_id, catalog_resp.id),
					eq(content.status, 'active')
				)
			)
			.execute()
			.then(take_uniq_or_throw)

		const genre_ids = await db
			.select()
			.from(catalog_genre)
			.where(eq(catalog_genre.catalog_id, catalog_resp.id))
			.execute()
			.then((res) => res.map((r) => r.genre_id))

		const genres = await db
			.select()
			.from(genre)
			.where(inArray(genre.id, genre_ids))
			.execute()
			.then((res) =>
				res.map((r) => ({
					slug: r.slug || '',
					name: r.name
				}))
			)

		const creator_ids = await db
			.select()
			.from(catalog_user)
			.where(eq(catalog_user.catalog_id, catalog_resp.id))
			.execute()
			.then((res) => res.map((r) => r.user_id))

		const creators = await treaty<UserApp>('http://localhost:3001')
			.users.get({
				query: {
					ids: creator_ids.join(',')
				}
			})
			.then((res) => res.data)

		return {
			id: catalog_resp.id,
			catalog_source: catalog_resp.source || 'original',
			catalog_play_url: `https://api.beta.noice.id/catalog/${catalog_resp.id}/play`,
			catalog_title: catalog_resp.title,
			catalog_type: catalog_resp.type || 'podcast',
			content_count: content_count.count,
			created_at: new Date(catalog_resp.created_at),
			album_cover: catalog_resp.image || '',
			genres: genres,
			creators: creators
				? creators.map((creator) => {
						return {
							display_name: creator.profile.display_name,
							user_name: creator.user_name,
							photo: creator.profile.photo,
							type: 'host'
						}
					})
				: []
		}
	}

	static async getCatalogByIds(
		{ ids }: { ids: string[] },
		{ db }: IMicroserviceContext
	): Promise<GetCatalogByIdResponse[]> {
		const catalogs = await db
			.select()
			.from(catalog)
			.where(and(inArray(catalog.id, ids), eq(catalog.status, 'active')))
			.execute()

		const results: GetCatalogByIdResponse[] = []

		for (const catalog_resp of catalogs) {
			const content_count = await db
				.select({
					count: count(content.id)
				})
				.from(content)
				.where(
					and(
						eq(content.catalog_id, catalog_resp.id),
						eq(content.status, 'active')
					)
				)
				.execute()
				.then(take_uniq_or_throw)

			const genre_ids = await db
				.select()
				.from(catalog_genre)
				.where(eq(catalog_genre.catalog_id, catalog_resp.id))
				.execute()
				.then((res) => res.map((r) => r.genre_id))

			const genres = await db
				.select()
				.from(genre)
				.where(inArray(genre.id, genre_ids))
				.execute()
				.then((res) =>
					res.map((r) => ({
						slug: r.slug || '',
						name: r.name
					}))
				)

			const creator_ids = await db
				.select()
				.from(catalog_user)
				.where(eq(catalog_user.catalog_id, catalog_resp.id))
				.execute()
				.then((res) => res.map((r) => r.user_id))

			const creators = await treaty<UserApp>('http://localhost:3001')
				.users.get({
					query: {
						ids: creator_ids.join(',')
					}
				})
				.then((res) => res.data)

			results.push({
				id: catalog_resp.id,
				catalog_source: catalog_resp.source || 'original',
				catalog_play_url: `https://api.beta.noice.id/catalog/${catalog_resp.id}/play`,
				catalog_title: catalog_resp.title,
				catalog_type: catalog_resp.type || 'podcast',
				content_count: content_count.count,
				created_at: new Date(catalog_resp.created_at),
				album_cover: catalog_resp.image || '',
				genres: genres,
				creators: creators
					? creators.map((creator) => {
							return {
								display_name: creator.profile.display_name,
								user_name: creator.user_name,
								photo: creator.profile.photo,
								type: 'host'
							}
						})
					: []
			})
		}

		return results
	}

	static async getContentStream(
		{ id, user_id }: { id: string; user_id: string },
		{ db, bearer_token }: IMicroserviceContext
	): Promise<GetContentStream | { message: string }> {
		const content_response = await db
			.select()
			.from(content)
			.where(and(eq(content.id, id), eq(content.status, 'active')))
			.execute()
			.then(take_uniq_or_throw)

		const purchased = await db
			.select()
			.from(user_premium_content)
			.where(
				and(
					eq(user_premium_content.entity_id, id),
					eq(user_premium_content.entity_type, 'content'),
					eq(user_premium_content.user_id, user_id),
					isNull(user_premium_content.deleted_at)
				)
			)
			.execute()
			.then((purchases) => {
				console.log(purchases)
				return !!(purchases[0] || null)
			})

		const included_in_subscription = await treaty<SubscriptionApp>(
			'http://localhost:3003'
		)
			['catalog-included'].get({
				query: { ids: content_response.catalog_id },
				headers: {
					Authorization: `Bearer ${bearer_token}`
				}
			})
			.then((res) => res.data?.[0].included_in_subscription)

		if (!content_response) {
			throw new NotFoundError()
		}

		let content_ownership: GetContentStream['content_ownership']

		if (purchased) {
			content_ownership = 'purchased'
		} else if (content_response.is_premium && included_in_subscription) {
			content_ownership = 'included_in_subscription'
		} else if (content_response.is_premium && !included_in_subscription) {
			content_ownership = 'none'
		} else {
			content_ownership = 'free'
		}

		const playable_full =
			content_ownership === 'purchased' ||
			content_ownership === 'free' ||
			content_ownership === 'included_in_subscription'

		const playable_preview =
			content_response.is_premium &&
			content_response.content_preview &&
			content_ownership === 'none'

		return {
			content_id: content_response.id,
			content_title: content_response.title,
			content_image: content_response.image || '',
			content_ownership,
			stream: {
				url: playable_full
					? content_response.url || ''
					: playable_preview
						? content_response.audio_preview_url || 'dummy link'
						: 'dummy link',
				video_url: playable_full
					? content_response.video_url || ''
					: playable_preview
						? content_response.video_preview_url || 'dummy link'
						: 'dummy link',
				default_tab: 'audio',
				total_duration: content_response.duration || 0,
				total_preview_duration: playable_preview
					? content_response.preview_time || 0
					: 0,
				total_played_duration: 0
			}
		}
	}
}

export default CatalogService
