import {
	subscription_catalogs,
	subscription_packages,
	user_subscription
} from '../database/schema'
import { IMicroserviceContext } from '../../../shared/interfaces'
import { and, eq, inArray } from 'drizzle-orm'
import { GetCatalogsIncludedSubscriptionResponse } from '../models'
import { take_uniq_or_throw } from '../database/utils'

abstract class SubscriptionService {
	static async getCatalogsIncludedSubscription(
		{ ids, user_id }: { ids: string[]; user_id: string },
		{ db }: IMicroserviceContext
	): Promise<GetCatalogsIncludedSubscriptionResponse> {
		// We currently only supports one subscription per user!
		const user_subscription_packages = await db
			.select()
			.from(user_subscription)
			.where(
				and(
					eq(user_subscription.user_id, user_id),
					eq(user_subscription.status, 'active')
				)
			)
			.execute()
			.then((res) => res.map((r) => r.package_id))

		// join subscription packages and subscription catalogs as its column include and exclude

		const subs_package = await db
			.select()
			.from(subscription_packages)
			.where(
				inArray(subscription_packages.id, user_subscription_packages)
			)
			.execute()
			.then(take_uniq_or_throw)

		const manually_included_catalogs = await db
			.select()
			.from(subscription_catalogs)
			.where(
				and(
					eq(subscription_catalogs.package_id, subs_package.id),
					eq(subscription_catalogs.status, 'include')
				)
			)
			.execute()

		const manually_excluded_catalogs = await db
			.select()
			.from(subscription_catalogs)
			.where(
				and(
					eq(subscription_catalogs.package_id, subs_package.id),
					eq(subscription_catalogs.status, 'exclude')
				)
			)
			.execute()

		return ids.map((id) => {
			if (catalog)
				return {
					catalog: id,
					included_in_subscription: manually_included_catalogs.some(
						(r) => r.catalog_id === id
					)
				}
		})
	}
}

export default SubscriptionService
